is_global = true
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = true
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = BoycottAppApi._0._1
build_property.RootNamespace = BoycottAppApi._0._1
build_property.ProjectDir = C:\Users\<USER>\Desktop\Balzor Apps\BoycottApp\BoycottAppApi.0.1\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\Desktop\Balzor Apps\BoycottApp\BoycottAppApi.0.1
build_property._RazorSourceGeneratorDebug = 
